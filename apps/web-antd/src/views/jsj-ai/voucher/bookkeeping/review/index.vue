<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  import { useTabbarStore, useUserStore } from '@vben/stores';

  import { LeftOutlined, PlusOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  import {
    generateVoucherPdf,
    updateVoucher,
    writeBackVouchers,
  } from '#/api/jsj-ai/api-v2';
  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';
  import { useCurrentCustomerStore } from '#/store/account-book/company';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';
  import { useVoucherStore } from '#/store/modules/voucher';

  import VoucherEditing from '../enter/VoucherEditing.vue';
  import AddAuxiliaryPop from '../enter/AddAuxiliaryPop.vue';
  import AddSubjectPop from '../enter/AddSubjectPop.vue';
  import OriginalFilesDisplay from './components/OriginalFilesDisplay.vue';

  const router = useRouter();
  const monthSelectionStore = useMonthSelectionStore();
  const voucherStore = useVoucherStore();
  const customerStore = useCurrentCustomerStore();
  const userStore = useUserStore();
  const tabbarStore = useTabbarStore();
  const { fetchCompanyNames, selectedCompany } = useCompanySelection();

  // 响应式数据
  const loading = ref(false);
  const voucherData = ref<any>(null);
  const originalFilesData = ref<any>(null);
  const voucherId = ref<string>('');
  const voucherEditingRef = ref<any>(null);
  const reviewVoucherInfo = ref<any>(null);
  const voucherEditingKey = ref<number>(0);
  const printLoading = ref(false);

  // 导航相关数据
  const navigationInfo = computed(() =>
    voucherStore.getCurrentVoucherPosition(),
  );

  // 防抖标记，避免快速重复触发
  const isNavigating = ref(false);

  // 获取辅助核算数据和科目数据
  const { assistantAccounting, subjectOptions } = useAccountSubjects();

  // 弹框引用
  const addSubjectRef = ref<any>(null);
  const addAuxiliaryRef = ref<any>(null);

  // 解析科目字符串，提取科目名称、代码、ID和辅助核算信息
  function parseSubjectString(subjectText: string, originalDetail: any) {
    console.log('🔍 解析科目字符串:', subjectText);
    console.log('📋 原始明细数据:', originalDetail);

    // 如果没有科目文本，返回原始数据
    if (!subjectText) {
      return {
        accountCode: originalDetail.account_code || '',
        accountId: originalDetail.account_id || '',
        accountName: originalDetail.account || '',
        auxiliaryInfo: originalDetail.auxiliary || [],
      };
    }

    // 科目字符串格式：科目代码 科目名称 [辅助核算代码 辅助核算名称]
    // 例如：1122 应收账款 00811 连云港沃至盛设备工程有限公司
    const parts = subjectText.split(' ').filter(Boolean);
    console.log('✂️ 科目字符串分割结果:', parts);

    if (parts.length < 2) {
      // 格式不正确，返回原始数据
      return {
        accountCode: originalDetail.account_code || '',
        accountId: originalDetail.account_id || '',
        accountName: originalDetail.account || subjectText,
        auxiliaryInfo: originalDetail.auxiliary || [],
      };
    }

    // 提取科目代码和名称
    const accountCode = parts[0];
    const accountName = parts[1];

    // 从会计科目数据中查找科目ID和辅助核算配置
    let accountId = originalDetail.account_id || '';
    let subjectUseAssistant = false;

    // 尝试从全局科目数据中查找匹配的科目信息
    if (subjectOptions.value && subjectOptions.value.length > 0) {
      const matchedSubject = subjectOptions.value.find(
        (option: any) =>
          option.code === accountCode && option.name === accountName,
      );
      if (matchedSubject) {
        // 如果找到匹配项，提取纯科目ID（去掉辅助核算的组合ID）
        accountId = matchedSubject.value.split('_')[0];
        subjectUseAssistant = matchedSubject.useAssistant;
        console.log('✅ 从科目数据中找到匹配的科目:', {
          accountId,
          useAssistant: subjectUseAssistant,
        });
      }
    }

    // 获取原始辅助核算数据
    const originalAuxiliary = originalDetail.auxiliary || [];
    console.log('🏷️ 原始辅助核算数据:', originalAuxiliary);

    // 如果有辅助核算信息（超过2个部分）
    if (parts.length > 2) {
      // 尝试匹配辅助核算代码或名称
      const auxiliaryCode = parts[2];
      const auxiliaryName = parts.slice(3).join(' '); // 剩余部分作为名称
      console.log(
        '🔍 提取的辅助核算 - 代码:',
        auxiliaryCode,
        '名称:',
        auxiliaryName,
      );

      // 在原始辅助核算中查找匹配项
      console.log(
        '🔍 开始匹配，查找条件 - 代码:',
        auxiliaryCode,
        '名称:',
        auxiliaryName,
      );

      // 首先尝试在原始数据中匹配
      let matchedAuxiliary = originalAuxiliary.find(
        (aux: any) => aux.code === auxiliaryCode || aux.name === auxiliaryName,
      );

      // 如果原始数据中没有匹配项，尝试从全局辅助核算数据中查找
      if (!matchedAuxiliary && assistantAccounting.value) {
        console.log('🔍 在全局辅助核算数据中查找匹配项');
        console.log('📋 全局辅助核算数据:', assistantAccounting.value);

        // 遍历所有类型的辅助核算数据
        for (const [type, items] of Object.entries(assistantAccounting.value)) {
          console.log(`🔍 查找类型: ${type}`, items);
          const found = items.find(
            (item: any) =>
              item.code === auxiliaryCode || item.name === auxiliaryName,
          );
          if (found) {
            matchedAuxiliary = {
              code: found.code,
              id: found.id,
              name: found.name,
              type,
            };
            console.log(`✅ 在 ${type} 中找到匹配项:`, matchedAuxiliary);
            break;
          }
        }
      }

      console.log('✅ 最终匹配到的辅助核算:', matchedAuxiliary);

      const result = {
        accountCode,
        accountId,
        accountName,
        auxiliaryInfo: matchedAuxiliary ? [matchedAuxiliary] : [],
      };
      console.log('📤 返回有辅助核算的结果:', result);
      return result;
    }

    // 没有辅助核算信息，检查科目是否需要辅助核算
    // 如果新科目不需要辅助核算，清空辅助核算数据；如果需要，保留原有数据
    const auxiliaryInfo = subjectUseAssistant ? originalAuxiliary : [];

    const result = {
      accountCode,
      accountId,
      accountName,
      auxiliaryInfo,
    };
    console.log(
      '📤 返回无辅助核算结果:',
      result,
      '科目是否需要辅助核算:',
      subjectUseAssistant,
    );
    return result;
  }

  // 键盘事件处理
  function handleKeyDown(event: KeyboardEvent) {
    // 防抖：如果正在导航中，忽略新的按键事件
    if (isNavigating.value) return;

    // 只在没有输入框聚焦时响应快捷键
    const activeElement = document.activeElement;
    const isInputFocused =
      activeElement &&
      (activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        (activeElement as HTMLElement).contentEditable === 'true');

    if (isInputFocused) return;

    // 凭证导航使用左右箭头键，原始文件导航使用 Ctrl + 左右箭头键
    switch (event.key) {
      case 'ArrowLeft': {
        if (!event.ctrlKey && !event.metaKey) {
          event.preventDefault();
          if (navigationInfo.value.hasPrevious) {
            handlePreviousVoucher();
          }
        }
        break;
      }
      case 'ArrowRight': {
        if (!event.ctrlKey && !event.metaKey) {
          event.preventDefault();
          if (navigationInfo.value.hasNext) {
            handleNextVoucher();
          }
        }
        break;
      }
    }
  }

  // 标记是否已添加事件监听器，避免重复添加
  let keydownListenerAdded = false;

  // 初始化页面数据
  onMounted(async () => {
    // 添加键盘事件监听（确保只添加一次）
    if (!keydownListenerAdded) {
      document.addEventListener('keydown', handleKeyDown);
      keydownListenerAdded = true;
    }

    // 获取公司列表
    try {
      await fetchCompanyNames();

      // 优先从store获取凭证数据
      if (voucherStore.reviewVoucherData) {
        reviewVoucherInfo.value = voucherStore.reviewVoucherData;
        voucherId.value = voucherStore.reviewVoucherData.id;

        // 设置凭证数据到VoucherEditing组件
        setVoucherDataToEditing(voucherStore.reviewVoucherData);

        // 直接从store中的originalData获取原始文件数据，无需重新调用API
        if (voucherStore.reviewVoucherData.originalData) {
          const originalData = voucherStore.reviewVoucherData.originalData;
          const originalApiData = originalData.originalApiData;

          if (originalApiData && originalApiData.source_info) {
            originalFilesData.value = transformSourceInfoToFilesData(
              originalApiData.source_info,
              originalApiData.source_type,
            );
          }
        }
      } else {
        // 如果store中没有数据，说明是直接访问编辑页面，需要先选择凭证
        message.warning('请先从凭证列表页面选择要编辑的凭证');
        router.push('/bookkeeping/view');
      }
    } catch (error) {
      console.error('初始化失败:', error);
      message.error('页面初始化失败');
    }
  });

  // 组件卸载时移除事件监听器
  onUnmounted(() => {
    if (keydownListenerAdded) {
      document.removeEventListener('keydown', handleKeyDown);
      keydownListenerAdded = false;
    }
  });

  // 保存编辑结果
  async function handleSaveReview() {
    if (!voucherEditingRef.value) {
      message.error('凭证编辑组件未加载');
      return;
    }

    try {
      loading.value = true;

      // 获取VoucherEditing组件中的数据
      const voucherEditingData = voucherEditingRef.value.state;

      if (!voucherEditingData || !voucherEditingData.list) {
        message.error('无法获取凭证数据');
        return;
      }

      // 验证凭证数据
      const validationResult = voucherEditingRef.value.data_validation();
      if (!validationResult.success) {
        message.error(`凭证数据验证失败：${validationResult.message}`);
        return;
      }

      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      if (!companyName || !month) {
        message.error('请先选择公司和月份');
        return;
      }

      if (!reviewVoucherInfo.value || !reviewVoucherInfo.value.id) {
        message.error('缺少凭证ID信息');
        return;
      }

      // 转换VoucherEditing组件的数据格式为API所需格式
      const details = voucherEditingData.list
        .filter((item: any) => item.subject && (item.borrower || item.lender))
        .map((item: any, index: number) => {
          // 从原始API数据中获取对应的明细信息
          const originalDetail =
            reviewVoucherInfo.value.originalData?.originalApiData?.voucher
              ?.details?.[index] || {};

          // 解析科目字符串，提取科目名称、代码、ID和辅助核算信息
          const { accountCode, accountId, accountName, auxiliaryInfo } =
            parseSubjectString(item.subject.text || '', originalDetail);

          return {
            account: accountName || originalDetail.account || '',
            account_code: accountCode || originalDetail.account_code || '',
            account_id: accountId || originalDetail.account_id || '',
            auxiliary: auxiliaryInfo || [],
            confidence: originalDetail.confidence || 1,
            credit: Number.parseFloat(item.lender) || 0,
            debit: Number.parseFloat(item.borrower) || 0,
            id: originalDetail.id || index + 1,
            summary: item.abstract?.text || originalDetail.summary || '',
            // 保留原始数据中的其他字段，如 origin_id
            ...(originalDetail.origin_id && {
              origin_id: originalDetail.origin_id,
            }),
          };
        });

      if (details.length === 0) {
        message.error('请至少添加一条有效的凭证明细');
        return;
      }

      // 计算借贷方合计
      const totalDebit = details.reduce(
        (sum: number, detail: any) => sum + detail.debit,
        0,
      );
      const totalCredit = details.reduce(
        (sum: number, detail: any) => sum + detail.credit,
        0,
      );

      // 从原始API数据中获取完整的凭证信息
      const originalVoucher =
        reviewVoucherInfo.value.originalData?.originalApiData?.voucher || {};

      // 构建更新凭证的参数，保持所有原始字段值不变，只更新details和计算字段
      const updateData = {
        details,
        record_date:
          originalVoucher.record_date ||
          reviewVoucherInfo.value.date ||
          new Date().toISOString().split('T')[0],
        timestamp: originalVoucher.timestamp || new Date().toISOString(),
        total_credit: totalCredit,
        total_debit: totalDebit,
        type: originalVoucher.type || reviewVoucherInfo.value.type || '记',
        unique_id: originalVoucher.unique_id || reviewVoucherInfo.value.id,
        voucher_num:
          originalVoucher.voucher_num ||
          reviewVoucherInfo.value.code?.split('-')[1] ||
          '001',
        write_back: originalVoucher.write_back || false,
      };

      console.log('保存凭证数据:', updateData);

      // 构建API调用参数，包含完整的原始数据结构
      const apiParams = {
        company_name: companyName,
        month,
        // 传递完整的凭证数据结构
        voucher: updateData,
        voucher_unique_id: updateData.unique_id,
      };

      console.log('API调用参数:', apiParams);

      // 调用updateVoucher API
      await updateVoucher(apiParams);

      message.success('凭证保存成功');

      // 更新本地数据
      if (reviewVoucherInfo.value) {
        reviewVoucherInfo.value.detail = details.map((detail: any) => ({
          credit: detail.credit,
          debit: detail.debit,
          id: detail.id,
          subjectName: detail.account,
          summary: detail.summary,
        }));
      }
    } catch (error) {
      console.error('保存凭证失败:', error);
      message.error('保存凭证失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 写入凭证
  async function handleWriteBackVoucher() {
    if (!reviewVoucherInfo.value || !reviewVoucherInfo.value.id) {
      message.warning('未找到要写入的凭证');
      return;
    }

    try {
      loading.value = true;

      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      if (!companyName || !month) {
        message.warning('请先选择公司和月份');
        return;
      }

      // 获取当前用户信息
      const username = userStore.userInfo?.username || '';

      if (!username) {
        message.error('无法获取用户信息，请重新登录');
        return;
      }

      // 调用凭证写入API
      await writeBackVouchers({
        company_name: companyName,
        month,
        username,
        voucher_ids: [reviewVoucherInfo.value.id],
      });

      message.success('凭证写入成功');

      // 可以选择跳转回查看凭证页面
      // router.push('/bookkeeping/view');
    } catch (error: any) {
      console.error('写入凭证失败:', error);
      message.error(`写入凭证失败：${error?.message || '未知错误'}`);
    } finally {
      loading.value = false;
    }
  }

  // 返回凭证查看页面
  async function handleReturnToView() {
    try {
      // 触发返回事件，通知查看页面恢复滚动位置
      window.dispatchEvent(new CustomEvent('voucher-return-from-review'));

      // 清除审核数据和导航数据
      voucherStore.clearReviewVoucherData();
      voucherStore.clearVoucherListAndIndex();

      // 先跳转到查看页面
      await router.push('/bookkeeping/view');

      // 跳转成功后关闭当前编辑页面的标签页
      await tabbarStore.closeTabByKey('/bookkeeping/review', router);
    } catch (error) {
      console.error('返回查看页面失败:', error);
      // 即使出错也要尝试跳转
      router.push('/bookkeeping/view');
    }
  }

  // 导航到上一张凭证
  function handlePreviousVoucher() {
    if (isNavigating.value) return; // 防抖

    const previousVoucher = voucherStore.getPreviousVoucher();
    if (previousVoucher) {
      navigateToVoucher(previousVoucher);
    } else {
      message.warning('已经是第一张凭证了');
    }
  }

  // 导航到下一张凭证
  function handleNextVoucher() {
    if (isNavigating.value) return; // 防抖

    const nextVoucher = voucherStore.getNextVoucher();
    if (nextVoucher) {
      navigateToVoucher(nextVoucher);
    } else {
      message.warning('已经是最后一张凭证了');
    }
  }

  // 打开新增科目弹框
  function openAddSubjectModal() {
    addSubjectRef.value?.open();
  }

  // 打开新增辅助核算弹框
  function openAddAuxiliaryModal() {
    addAuxiliaryRef.value?.open();
  }

  // 刷新数据
  function handleRefreshData() {
    // 刷新科目和辅助核算数据
    console.log('刷新科目和辅助核算数据');
  }

  // 导航到指定凭证
  function navigateToVoucher(voucher: any) {
    // 设置导航状态，防止重复触发
    isNavigating.value = true;

    try {
      // 更新store中的审核凭证数据
      voucherStore.setReviewVoucherData({
        code: voucher.code,
        confirmed: voucher.confirmed,
        credit: voucher.credit || 0,
        date: voucher.date,
        debit: voucher.debit || 0,
        detail: voucher.detail,
        executor: voucher.executor,
        id: voucher.id,
        originalData: voucher, // 保存完整的原始数据
        source_type: voucher.source_type,
        totalAmount: voucher.totalAmount,
        type: voucher.type,
      });

      // 更新当前凭证索引
      voucherStore.setVoucherListWithIndex(
        voucherStore.voucherList,
        voucher.id,
      );
    } finally {
      // 延迟重置导航状态，避免快速重复触发
      setTimeout(() => {
        isNavigating.value = false;
      }, 300);
    }
  }

  // 设置凭证数据到编辑组件
  function setVoucherDataToEditing(reviewData: any) {
    // 转换数据格式以适配VoucherEditing组件的ListItm格式
    const voucherEditingData = reviewData.detail.map((item: any) => {
      // 修改逻辑：支持负数金额显示
      const borrowerValue =
        item.debit !== 0 && item.debit !== null && item.debit !== undefined
          ? item.debit.toString()
          : '';
      const lenderValue =
        item.credit !== 0 && item.credit !== null && item.credit !== undefined
          ? item.credit.toString()
          : '';

      return {
        abstract: { text: item.summary }, // 摘要对象格式
        balance: 0, // 余额
        borrower: borrowerValue, // 借方金额
        lender: lenderValue, // 贷方金额
        subject: {
          balance: 0, // 默认余额
          code: '', // 科目代码
          // 添加数量和单价信息
          quantity: item.quantity || null,
          text: item.subjectName,
          unit_price: item.unit_price || null,
        }, // 科目对象格式
      };
    });

    // 确保至少有4行数据（VoucherEditing组件的默认要求）
    while (voucherEditingData.length < 4) {
      voucherEditingData.push({
        abstract: undefined,
        balance: 0,
        borrower: '',
        lender: '',
        subject: undefined,
      });
    }

    // 将数据存储到localStorage，这样VoucherEditing组件就能读取到
    const storageKey = `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher`;
    const customerIdKey = `${import.meta.env.VITE_APP_NAMESPACE}-current-voucher-customer_id`;

    localStorage.setItem(storageKey, JSON.stringify(voucherEditingData));

    // 重要：同时设置公司ID，确保VoucherEditing组件能正确读取数据
    // VoucherEditing组件使用useCurrentCustomerStore().customerId进行验证
    // 但是useCurrentCustomerStore没有customerId属性，我们需要创建一个兼容的值
    const currentCompanyName = selectedCompany.value;

    // 为了兼容VoucherEditing组件的验证逻辑，我们需要确保customerId有值
    // 如果customerStore.currentCompany存在，使用其id，否则使用公司名称
    const customerId =
      customerStore.currentCompany?.id || currentCompanyName || 'default';

    // 同时，我们需要确保customerStore有customerId属性供VoucherEditing组件使用
    // 这是一个临时解决方案，直到VoucherEditing组件被修复
    (customerStore as any).customerId = customerId;

    localStorage.setItem(customerIdKey, customerId);

    // 同时设置voucherData用于显示
    voucherData.value = {
      code: reviewData.code,
      date: reviewData.date,
      details: reviewData.detail,
      id: reviewData.id,
      total_credit: reviewData.credit,
      total_debit: reviewData.debit,
      type: reviewData.type,
    };

    // 更新key来强制重新渲染VoucherEditing组件
    setTimeout(() => {
      voucherEditingKey.value += 1;
    }, 100);
  }

  // 转换source_info为OriginalFilesDisplay组件期望的格式
  function transformSourceInfoToFilesData(sourceInfo: any, sourceType: string) {
    console.log('转换原始文件数据:', sourceInfo, sourceType);

    if (!sourceInfo) {
      return null;
    }

    const filesData: any = {};

    // 处理发票信息
    if (sourceInfo.invoice_info) {
      const invoiceInfo = sourceInfo.invoice_info;

      // 根据source_type判断是进项还是销项发票
      if (sourceType === '一般进项' || sourceType === '进项发票') {
        filesData.input_invoice = [invoiceInfo];
        console.log('设置进项发票数据:', filesData.input_invoice);
      } else if (sourceType === '销项发票' || sourceType === '一般销项') {
        filesData.output_invoice = [invoiceInfo];
        console.log('设置销项发票数据:', filesData.output_invoice);
      } else {
        // 默认作为进项发票处理
        filesData.input_invoice = [invoiceInfo];
        console.log('默认设置为进项发票数据:', filesData.input_invoice);
      }
    }

    // 处理银行回单信息
    if (sourceInfo.bank_receipt_info) {
      filesData.bank_receipt_info = sourceInfo.bank_receipt_info;
    }

    // 处理银行回单数组
    if (sourceInfo.bank_receipt && Array.isArray(sourceInfo.bank_receipt)) {
      filesData.bank_receipt = sourceInfo.bank_receipt;
    }

    // 处理工资单信息
    if (sourceInfo.payroll_info) {
      filesData.payroll_info = Array.isArray(sourceInfo.payroll_info)
        ? sourceInfo.payroll_info
        : [sourceInfo.payroll_info];
    }

    console.log('转换后的文件数据:', filesData);
    return filesData;
  }

  // 预览当前凭证
  async function handlePrintCurrentVoucher() {
    if (!reviewVoucherInfo.value || !reviewVoucherInfo.value.id) {
      message.error('未找到要预览的凭证');
      return;
    }

    printLoading.value = true;
    try {
      console.log('开始生成凭证预览:', reviewVoucherInfo.value.id);

      const response = await generateVoucherPdf({
        voucher_id: reviewVoucherInfo.value.id,
      });

      const previewUrl = response;

      // 在新窗口中打开PDF预览，不触发下载
      window.open(previewUrl, '_blank');

      message.success('凭证PDF预览已打开');
    } catch (error: any) {
      console.error('生成凭证预览失败:', error);
      message.error(error?.message || '生成凭证预览失败，请重试');
    } finally {
      printLoading.value = false;
    }
  }

  // 监听store中编辑凭证数据的变化
  watch(
    () => voucherStore.reviewVoucherData,
    (newData) => {
      if (newData) {
        reviewVoucherInfo.value = newData;
        voucherId.value = newData.id;
        setVoucherDataToEditing(newData);

        // 直接从store中的originalData获取原始文件数据
        if (newData.originalData && newData.originalData.originalApiData) {
          const originalApiData = newData.originalData.originalApiData;
          if (originalApiData.source_info) {
            originalFilesData.value = transformSourceInfoToFilesData(
              originalApiData.source_info,
              originalApiData.source_type,
            );
          }
        }
      }
    },
    { immediate: true },
  );
</script>

<template>
  <div class="voucher-review-page">
    <!-- 页面标题栏 -->
    <a-card class="review-header" :bordered="false">
      <div class="header-content">
        <div class="header-title">
          <h2>凭证编辑</h2>
          <div v-if="reviewVoucherInfo" class="voucher-info">
            <a-tag color="blue">{{ reviewVoucherInfo.code }}</a-tag>
            <a-tag color="green">{{ reviewVoucherInfo.date }}</a-tag>
            <a-tag :color="reviewVoucherInfo.confirmed ? 'success' : 'warning'">
              {{ reviewVoucherInfo.confirmed ? '已确认' : '待确认' }}
            </a-tag>
            <!-- 凭证导航信息 -->
            <a-tag v-if="navigationInfo.total > 0" color="purple">
              {{ navigationInfo.current }} / {{ navigationInfo.total }}
            </a-tag>
            <!-- 键盘快捷键提示 -->
            <a-tooltip
              v-if="navigationInfo.total > 1"
              title="使用左右箭头键快速切换凭证"
            >
              <a-tag color="default" style="cursor: help">⌨️ ← →</a-tag>
            </a-tooltip>
          </div>
          <a-tag v-else-if="voucherId" color="default">
            凭证ID: {{ voucherId }}
          </a-tag>
        </div>
        <a-space>
          <!-- 新增科目按钮 -->
          <a-button size="small" type="default" @click="openAddSubjectModal">
            <template #icon>
              <PlusOutlined />
            </template>
            新增科目
          </a-button>

          <!-- 新增辅助核算按钮 -->
          <a-button size="small" type="default" @click="openAddAuxiliaryModal">
            <template #icon>
              <PlusOutlined />
            </template>
            新增辅助
          </a-button>

          <a-divider type="vertical" />

          <!-- 凭证导航按钮 -->
          <a-button-group v-if="navigationInfo.total > 1">
            <a-button
              size="small"
              :disabled="!navigationInfo.hasPrevious"
              @click="handlePreviousVoucher"
            >
              <template #icon>
                <LeftOutlined />
              </template>
              上一张
            </a-button>
            <a-button
              size="small"
              :disabled="!navigationInfo.hasNext"
              @click="handleNextVoucher"
            >
              下一张
              <template #icon>
                <RightOutlined />
              </template>
            </a-button>
          </a-button-group>

          <a-divider v-if="navigationInfo.total > 1" type="vertical" />

          <a-button size="small" @click="handleReturnToView">返回查看</a-button>
          <a-button size="small" type="primary" @click="handleSaveReview">
            保存
          </a-button>
          <a-button
            size="small"
            type="default"
            @click="handlePrintCurrentVoucher"
            :loading="printLoading"
          >
            {{ printLoading ? '打印中...' : '打印凭证' }}
          </a-button>
          <a-button
            size="small"
            type="primary"
            @click="handleWriteBackVoucher"
            :loading="loading"
          >
            写入凭证
          </a-button>
        </a-space>
      </div>
    </a-card>

    <!-- 主要内容区域 -->
    <div class="review-content">
      <!-- 凭证录入区域 -->
      <a-card title="凭证录入" class="voucher-editing-card" :bordered="false">
        <template #extra>
          <span class="card-desc">编辑和完善凭证信息</span>
        </template>
        <VoucherEditing ref="voucherEditingRef" :key="voucherEditingKey" />
      </a-card>

      <!-- 原始文件展示区域 -->
      <a-card title="原始文件" class="original-files-card" :bordered="false">
        <template #extra>
          <span class="card-desc">查看相关的原始凭证文件</span>
        </template>
        <OriginalFilesDisplay
          v-if="originalFilesData"
          :files-data="originalFilesData"
        />
        <a-empty v-else description="暂无原始文件" />
      </a-card>
    </div>

    <!-- 新增科目弹框 -->
    <AddSubjectPop ref="addSubjectRef" @refresh="handleRefreshData" />

    <!-- 新增辅助核算弹框 -->
    <AddAuxiliaryPop ref="addAuxiliaryRef" @refresh="handleRefreshData" />
  </div>
</template>

<style lang="scss" scoped>
  // 响应式设计
  @media (max-width: 768px) {
    .voucher-review-page {
      gap: 6px;
      padding: 6px;
    }

    .review-header {
      :deep(.ant-card-body) {
        padding: 8px 12px;
      }

      .header-content {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;

        .header-title {
          flex-direction: column;
          gap: 6px;
          align-items: flex-start;

          h2 {
            font-size: 14px;
          }
        }
      }
    }

    .voucher-editing-card,
    .original-files-card {
      :deep(.ant-card-head) {
        min-height: 36px;
        padding: 0 12px;

        .ant-card-head-title {
          padding: 6px 0;
          font-size: 13px;
        }

        .ant-card-extra {
          padding: 6px 0;
        }
      }

      :deep(.ant-card-body) {
        padding: 8px;
      }

      .card-desc {
        font-size: 10px;
      }
    }
  }

  // 小屏幕优化
  @media (max-width: 480px) {
    .voucher-review-page {
      gap: 4px;
      padding: 4px;
    }

    .review-header {
      :deep(.ant-card-body) {
        padding: 6px 8px;
      }

      .header-content {
        .header-title h2 {
          font-size: 13px;
        }
      }
    }

    .voucher-editing-card,
    .original-files-card {
      :deep(.ant-card-head) {
        min-height: 32px;
        padding: 0 8px;

        .ant-card-head-title {
          padding: 4px 0;
          font-size: 12px;
        }
      }

      :deep(.ant-card-body) {
        padding: 6px;
      }
    }
  }

  .voucher-review-page {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 100vh;
    padding: 8px;
    background: #f5f5f5;
  }

  .review-header {
    :deep(.ant-card-body) {
      padding: 12px 16px;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-title {
        display: flex;
        gap: 8px;
        align-items: center;

        h2 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        .voucher-info {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          align-items: center;
        }
      }
    }
  }

  .review-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .voucher-editing-card {
    :deep(.ant-card-head) {
      min-height: 40px;
      padding: 0 16px;

      .ant-card-head-title {
        padding: 8px 0;
        font-size: 14px;
        font-weight: 500;
      }

      .ant-card-extra {
        padding: 8px 0;
      }
    }

    :deep(.ant-card-body) {
      padding: 12px;
    }

    .card-desc {
      font-size: 11px;
      color: #8c8c8c;
    }
  }

  .original-files-card {
    :deep(.ant-card-head) {
      min-height: 40px;
      padding: 0 16px;

      .ant-card-head-title {
        padding: 8px 0;
        font-size: 14px;
        font-weight: 500;
      }

      .ant-card-extra {
        padding: 8px 0;
      }
    }

    :deep(.ant-card-body) {
      padding: 12px;
    }

    .card-desc {
      font-size: 11px;
      color: #8c8c8c;
    }
  }
</style>
